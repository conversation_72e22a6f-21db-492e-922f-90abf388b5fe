import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { Zap, Eye, Shield, Camera, Upload } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { HairZone, ZoneColorAnalysis, ZonePhysicalAnalysis } from '@/types/hair-diagnosis';
import { CapturedPhoto, PhotoAngle } from '@/types/photo-capture';
import { ServiceData } from '../hooks/useServiceFlow';
import { usePhotoAnalysis } from '../hooks/usePhotoAnalysis';
import { calculateDiagnosisProgress } from '../utils/serviceValidations';

// Import existing components
import ClientHistoryPanel from '@/components/ClientHistoryPanel';
import DiagnosisSelector from '@/components/DiagnosisSelector';
import ZoneDiagnosisForm from '@/components/ZoneDiagnosisForm';
import PhotoGallery from '@/components/PhotoGallery';
import GuidedCamera from '@/components/GuidedCamera';

interface DiagnosisStepProps {
  data: ServiceData;
  onUpdate: (updates: Partial<ServiceData>) => void;
  onNext: () => void;
  onBack: () => void;
  onSave?: () => void;
}

export const DiagnosisStep: React.FC<DiagnosisStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack,
  onSave
}) => {
  const [diagnosisTab, setDiagnosisTab] = useState<"roots" | "mids" | "ends">("roots");
  const [showHistoryPanel, setShowHistoryPanel] = useState(true);
  const [isDataFromAI, setIsDataFromAI] = useState(false);
  
  const isMounted = useRef(true);

  const {
    showGuidedCamera,
    setShowGuidedCamera,
    currentPhotoAngle,
    setCurrentPhotoAngle,
    cameraMode,
    setCameraMode,
    cameraActive,
    setCameraActive,
    isAnalyzing,
    analysisResult,
    privacyMode,
    setPrivacyMode,
    clearAnalysis,
    performAnalysis,
    pickMultipleImages,
    handleCameraCapture
  } = usePhotoAnalysis();

  const handleAIAnalysis = async () => {
    if (data.hairPhotos.length === 0) {
      Alert.alert("Error", "Por favor captura al menos una foto del cabello");
      return;
    }
    
    try {
      const message = await performAnalysis(data.hairPhotos);
      
      // Update fields with AI results if available
      if (analysisResult) {
        onUpdate({
          hairThickness: analysisResult.hairThickness,
          hairDensity: analysisResult.hairDensity,
          overallTone: analysisResult.overallTone,
          overallUndertone: analysisResult.overallUndertone,
          lastChemicalProcessType: analysisResult.detectedChemicalProcess || data.lastChemicalProcessType,
          lastChemicalProcessDate: analysisResult.estimatedLastProcessDate || data.lastChemicalProcessDate,
          zoneColorAnalysis: {
            [HairZone.ROOTS]: {
              zone: HairZone.ROOTS,
              depthLevel: analysisResult.zoneAnalysis.roots.depth || analysisResult.zoneAnalysis.roots.depthLevel,
              tone: analysisResult.zoneAnalysis.roots.tone as any,
              undertone: analysisResult.zoneAnalysis.roots.undertone as any,
              state: analysisResult.zoneAnalysis.roots.state as any,
              grayPercentage: analysisResult.zoneAnalysis.roots.grayPercentage || 0,
              grayType: analysisResult.zoneAnalysis.roots.grayType as any,
              grayPattern: analysisResult.zoneAnalysis.roots.grayPattern as any,
              unwantedTone: analysisResult.zoneAnalysis.roots.unwantedTone as any,
              cuticleState: analysisResult.zoneAnalysis.roots.cuticleState as any,
              damage: analysisResult.zoneAnalysis.roots.damage as any,
            },
            [HairZone.MIDS]: {
              zone: HairZone.MIDS,
              depthLevel: analysisResult.zoneAnalysis.mids.depth || analysisResult.zoneAnalysis.mids.depthLevel,
              tone: analysisResult.zoneAnalysis.mids.tone as any,
              undertone: analysisResult.zoneAnalysis.mids.undertone as any,
              state: analysisResult.zoneAnalysis.mids.state as any,
              unwantedTone: analysisResult.zoneAnalysis.mids.unwantedTone as any,
              pigmentAccumulation: analysisResult.zoneAnalysis.mids.pigmentAccumulation as any,
              cuticleState: analysisResult.zoneAnalysis.mids.cuticleState as any,
              demarkationBands: analysisResult.zoneAnalysis.mids.demarkationBands || [],
              damage: analysisResult.zoneAnalysis.mids.damage as any,
            },
            [HairZone.ENDS]: {
              zone: HairZone.ENDS,
              depthLevel: analysisResult.zoneAnalysis.ends.depth || analysisResult.zoneAnalysis.ends.depthLevel,
              tone: analysisResult.zoneAnalysis.ends.tone as any,
              undertone: analysisResult.zoneAnalysis.ends.undertone as any,
              state: analysisResult.zoneAnalysis.ends.state as any,
              unwantedTone: analysisResult.zoneAnalysis.ends.unwantedTone as any,
              pigmentAccumulation: analysisResult.zoneAnalysis.ends.pigmentAccumulation as any,
              cuticleState: analysisResult.zoneAnalysis.ends.cuticleState as any,
              damage: analysisResult.zoneAnalysis.ends.damage as any,
            }
          },
          zonePhysicalAnalysis: {
            [HairZone.ROOTS]: {
              zone: HairZone.ROOTS,
              porosity: analysisResult.zoneAnalysis.roots.porosity as any,
              elasticity: analysisResult.zoneAnalysis.roots.elasticity as any,
              resistance: analysisResult.zoneAnalysis.roots.resistance as any,
              damage: analysisResult.zoneAnalysis.roots.damage as any
            },
            [HairZone.MIDS]: {
              zone: HairZone.MIDS,
              porosity: analysisResult.zoneAnalysis.mids.porosity as any,
              elasticity: analysisResult.zoneAnalysis.mids.elasticity as any,
              resistance: analysisResult.zoneAnalysis.mids.resistance as any,
              damage: analysisResult.zoneAnalysis.mids.damage as any
            },
            [HairZone.ENDS]: {
              zone: HairZone.ENDS,
              porosity: analysisResult.zoneAnalysis.ends.porosity as any,
              elasticity: analysisResult.zoneAnalysis.ends.elasticity as any,
              resistance: analysisResult.zoneAnalysis.ends.resistance as any,
              damage: analysisResult.zoneAnalysis.ends.damage as any
            }
          }
        });
        
        setIsDataFromAI(true);
      }
      
      onSave?.();
      return message;
    } catch (error: any) {
      if (error.message === 'TIMEOUT_ERROR') {
        Alert.alert(
          "Análisis tardando más de lo normal",
          "El análisis está tardando más de 30 segundos. ¿Qué deseas hacer?",
          [
            { 
              text: "Continuar esperando", 
              onPress: () => handleAIAnalysis()
            },
            { 
              text: "Diagnóstico manual", 
              onPress: () => {
                onUpdate({ diagnosisMethod: "manual" });
              },
              style: "cancel"
            }
          ]
        );
      } else {
        Alert.alert(
          "Error en el análisis",
          "No se pudo completar el análisis. Por favor intenta nuevamente.",
          [{ text: "OK" }]
        );
      }
    }
  };

  const handleMultipleImagePick = async () => {
    const message = await pickMultipleImages(data.hairPhotos, (photos) => {
      onUpdate({ hairPhotos: photos });
    }, onSave);
    
    if (message) {
      // Show toast message (you might want to pass this up to parent)
      console.log(message);
    }
  };

  const handleCameraOpen = (angle: PhotoAngle) => {
    setCurrentPhotoAngle(angle);
    setCameraMode('diagnosis');
    setCameraActive(true);
    setShowGuidedCamera(true);
  };

  const handlePhotoCaptured = (uri: string, angle: PhotoAngle, quality: any) => {
    const message = handleCameraCapture(
      uri, 
      angle, 
      quality, 
      data.hairPhotos, 
      (photos) => onUpdate({ hairPhotos: photos }),
      onSave
    );
    
    if (message) {
      console.log(message);
    }
  };

  const handleRecommendationApply = (recommendation: any) => {
    // Apply recommendation to current data
    if (recommendation.type === 'brand_preference') {
      onUpdate({ selectedBrand: recommendation.value });
    }
    // Add more recommendation types as needed
  };

  const renderPrivacyBanner = () => (
    <View style={styles.privacyBanner}>
      <Shield size={16} color={Colors.light.success} />
      <Text style={styles.privacyBannerText}>
        🔒 PRIVACIDAD: Las imágenes se procesan con difuminado facial automático y se eliminan inmediatamente después del análisis.
      </Text>
    </View>
  );

  const currentZone = diagnosisTab === "roots" ? HairZone.ROOTS : 
                     diagnosisTab === "mids" ? HairZone.MIDS : HairZone.ENDS;
  
  const progressData = calculateDiagnosisProgress(
    data.hairThickness,
    data.hairDensity,
    data.overallTone,
    data.overallUndertone,
    data.zoneColorAnalysis,
    data.zonePhysicalAnalysis,
    data.hairPhotos
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.stepContainer}>
        <View style={styles.diagnosisHeader}>
          <View>
            <Text style={styles.stepTitle}>Diagnóstico Capilar Ultra-Inteligente</Text>
            {data.client && (
              <Text style={styles.clientName}>Cliente: {data.client?.name || 'Cliente'}</Text>
            )}
          </View>
        </View>

        {renderPrivacyBanner()}

        {/* Client History Panel */}
        {data.clientId && showHistoryPanel && (
          <ClientHistoryPanel 
            clientId={data.clientId}
            onRecommendationApply={handleRecommendationApply}
          />
        )}

        <View style={styles.tabsContainer}>
          <TouchableOpacity 
            style={[styles.tab, data.diagnosisMethod === "ai" && styles.activeTab]}
            onPress={() => onUpdate({ diagnosisMethod: "ai" })}
          >
            <Zap size={16} color={data.diagnosisMethod === "ai" ? Colors.light.primary : Colors.light.gray} />
            <Text style={[styles.tabText, data.diagnosisMethod === "ai" && styles.activeTabText]}>
              Con IA ✨
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.tab, data.diagnosisMethod === "manual" && styles.activeTab]}
            onPress={() => onUpdate({ diagnosisMethod: "manual" })}
          >
            <Eye size={16} color={data.diagnosisMethod === "manual" ? Colors.light.primary : Colors.light.gray} />
            <Text style={[styles.tabText, data.diagnosisMethod === "manual" && styles.activeTabText]}>
              Manual
            </Text>
          </TouchableOpacity>
        </View>

        {/* AI Mode */}
        {data.diagnosisMethod === "ai" && (
          <>
            <Text style={styles.sectionTitle}>Fotografías del cabello (3-5 imágenes)</Text>
            
            <PhotoGallery
              photos={data.hairPhotos}
              onAddPhoto={handleMultipleImagePick}
              onCameraCapture={handleCameraOpen}
              onRemovePhoto={(photoId) => {
                const updatedPhotos = data.hairPhotos.filter(p => p.id !== photoId);
                onUpdate({ hairPhotos: updatedPhotos });
              }}
              maxPhotos={5}
              showQuality={true}
            />

            {data.hairPhotos.length > 0 && (
              <TouchableOpacity 
                style={[styles.aiButton, isAnalyzing && styles.aiButtonDisabled]}
                onPress={handleAIAnalysis}
                disabled={isAnalyzing}
              >
                {isAnalyzing ? (
                  <>
                    <ActivityIndicator size="small" color="white" />
                    <Text style={styles.aiButtonText}>Analizando cabello...</Text>
                  </>
                ) : (
                  <>
                    <Zap size={16} color="white" />
                    <Text style={styles.aiButtonText}>Analizar con IA</Text>
                  </>
                )}
              </TouchableOpacity>
            )}
          </>
        )}

        {/* Manual Mode or AI Results */}
        {(data.diagnosisMethod === "manual" || analysisResult) && (
          <>
            {/* General characteristics */}
            <DiagnosisSelector
              hairThickness={data.hairThickness}
              hairDensity={data.hairDensity}
              overallTone={data.overallTone}
              overallUndertone={data.overallUndertone}
              onHairThicknessChange={(value) => onUpdate({ hairThickness: value })}
              onHairDensityChange={(value) => onUpdate({ hairDensity: value })}
              onOverallToneChange={(value) => onUpdate({ overallTone: value })}
              onOverallUndertoneChange={(value) => onUpdate({ overallUndertone: value })}
              isFromAI={isDataFromAI}
            />

            {/* Zone tabs */}
            <View style={styles.zoneTabs}>
              <TouchableOpacity 
                style={[styles.zoneTab, diagnosisTab === "roots" && styles.activeZoneTab]}
                onPress={() => setDiagnosisTab("roots")}
              >
                <Text style={[styles.zoneTabText, diagnosisTab === "roots" && styles.activeZoneTabText]}>
                  Raíces
                </Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.zoneTab, diagnosisTab === "mids" && styles.activeZoneTab]}
                onPress={() => setDiagnosisTab("mids")}
              >
                <Text style={[styles.zoneTabText, diagnosisTab === "mids" && styles.activeZoneTabText]}>
                  Medios
                </Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.zoneTab, diagnosisTab === "ends" && styles.activeZoneTab]}
                onPress={() => setDiagnosisTab("ends")}
              >
                <Text style={[styles.zoneTabText, diagnosisTab === "ends" && styles.activeZoneTabText]}>
                  Puntas
                </Text>
              </TouchableOpacity>
            </View>

            {/* Zone-specific diagnosis */}
            {currentZone && (
              <ZoneDiagnosisForm
                zone={currentZone}
                colorAnalysis={data.zoneColorAnalysis[currentZone] || {}}
                physicalAnalysis={data.zonePhysicalAnalysis[currentZone] || {}}
                onColorChange={(analysis) => {
                  onUpdate({
                    zoneColorAnalysis: {
                      ...data.zoneColorAnalysis,
                      [currentZone]: { ...data.zoneColorAnalysis[currentZone], ...analysis }
                    }
                  });
                }}
                onPhysicalChange={(analysis) => {
                  onUpdate({
                    zonePhysicalAnalysis: {
                      ...data.zonePhysicalAnalysis,
                      [currentZone]: { ...data.zonePhysicalAnalysis[currentZone], ...analysis }
                    }
                  });
                }}
                isFromAI={isDataFromAI}
              />
            )}
          </>
        )}

        {/* Guided Camera Modal */}
        {cameraMode === 'diagnosis' && (
          <GuidedCamera
            visible={showGuidedCamera}
            active={cameraActive}
            currentAngle={currentPhotoAngle}
            mode={cameraMode}
            onCapture={handlePhotoCaptured}
            onClose={() => {
              setShowGuidedCamera(false);
              setCameraActive(false);
              setCameraMode(null);
            }}
            onSkip={() => {
              // Find next angle for diagnosis photos
              const capturedAngles = data.hairPhotos.map(p => p.angle);
              const nextGuide = require('@/types/photo-capture').PHOTO_GUIDES.find((g: any) => 
                g.angle !== currentPhotoAngle && !capturedAngles.includes(g.angle)
              );
              if (nextGuide) {
                setCurrentPhotoAngle(nextGuide.angle);
              } else {
                setShowGuidedCamera(false);
                setCameraActive(false);
                setCameraMode(null);
              }
            }}
          />
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepContainer: {
    padding: 15,
  },
  diagnosisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 5,
  },
  clientName: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 15,
  },
  privacyBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.light.success + "15",
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.success,
  },
  privacyBannerText: {
    fontSize: 12,
    color: Colors.light.success,
    marginLeft: 8,
    flex: 1,
    fontWeight: "500",
  },
  tabsContainer: {
    flexDirection: "row",
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 4,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
    borderRadius: 12,
  },
  activeTab: {
    backgroundColor: "white",
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  tabText: {
    fontSize: 15,
    fontWeight: "500",
    color: Colors.light.gray,
  },
  activeTabText: {
    color: Colors.light.primary,
    fontWeight: "700",
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  aiButton: {
    backgroundColor: Colors.light.accent,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginBottom: 20,
    shadowColor: Colors.light.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
  },
  aiButtonDisabled: {
    backgroundColor: Colors.light.gray,
    shadowOpacity: 0,
    elevation: 0,
  },
  aiButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  zoneTabs: {
    flexDirection: "row",
    backgroundColor: "white",
    borderRadius: 8,
    marginBottom: 20,
  },
  zoneTab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
  },
  activeZoneTab: {
    backgroundColor: "rgba(106, 61, 232, 0.1)",
  },
  zoneTabText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  activeZoneTabText: {
    color: Colors.light.primary,
    fontWeight: "600",
  },
});
