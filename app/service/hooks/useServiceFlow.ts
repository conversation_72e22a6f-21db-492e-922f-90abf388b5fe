import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { 
  HairZone, 
  ZoneColorAnalysis, 
  ZonePhysicalAnalysis 
} from '@/types/hair-diagnosis';
import { CapturedPhoto } from '@/types/photo-capture';
import { DesiredPhoto } from '@/types/desired-photo';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import { ViabilityAnalysis, FormulaCost } from '@/types/formulation';

// Define the steps of the consultation flow
export const STEPS = [
  { id: "diagnosis", title: "Diagnóstico Capilar" },
  { id: "desired", title: "Resultado Deseado" },
  { id: "formulation", title: "Formulación" },
  { id: "result", title: "Resultado Final" }
];

export interface ServiceData {
  // Client info
  client: any;
  clientId: string | null;
  
  // Diagnosis data
  diagnosisMethod: string;
  hairPhotos: CapturedPhoto[];
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallUndertone: string;
  lastChemicalProcessType: string;
  lastChemicalProcessDate: string;
  diagnosisNotes: string;
  zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>;
  zonePhysicalAnalysis: Record<HairZone, Partial<ZonePhysicalAnalysis>>;
  
  // Desired result data
  desiredMethod: string;
  desiredPhotos: DesiredPhoto[];
  desiredAnalysisResult: DesiredColorAnalysisResult | null;
  desiredNotes: string;
  
  // Formulation data
  selectedBrand: string;
  selectedLine: string;
  formula: string;
  isFormulaFromAI: boolean;
  formulaCost: FormulaCost | null;
  viabilityAnalysis: ViabilityAnalysis | null;
  stockValidation: {
    isChecking: boolean;
    hasStock: boolean;
    missingProducts: string[];
    checked: boolean;
  };
  
  // Result data
  resultImage: string | null;
  clientSatisfaction: number;
  resultNotes: string;
}

export const useServiceFlow = () => {
  const [currentStep, setCurrentStep] = useState(0);
  
  const [serviceData, setServiceData] = useState<ServiceData>({
    // Client info
    client: null,
    clientId: null,
    
    // Diagnosis data
    diagnosisMethod: "ai",
    hairPhotos: [],
    hairThickness: "",
    hairDensity: "",
    overallTone: "",
    overallUndertone: "",
    lastChemicalProcessType: "",
    lastChemicalProcessDate: "",
    diagnosisNotes: "",
    zoneColorAnalysis: {
      [HairZone.ROOTS]: { zone: HairZone.ROOTS },
      [HairZone.MIDS]: { zone: HairZone.MIDS },
      [HairZone.ENDS]: { zone: HairZone.ENDS }
    },
    zonePhysicalAnalysis: {
      [HairZone.ROOTS]: { zone: HairZone.ROOTS },
      [HairZone.MIDS]: { zone: HairZone.MIDS },
      [HairZone.ENDS]: { zone: HairZone.ENDS }
    },
    
    // Desired result data
    desiredMethod: "ai",
    desiredPhotos: [],
    desiredAnalysisResult: null,
    desiredNotes: "",
    
    // Formulation data
    selectedBrand: "Wella Professionals",
    selectedLine: "Illumina Color",
    formula: "",
    isFormulaFromAI: true,
    formulaCost: null,
    viabilityAnalysis: null,
    stockValidation: {
      isChecking: false,
      hasStock: true,
      missingProducts: [],
      checked: false,
    },
    
    // Result data
    resultImage: null,
    clientSatisfaction: 5,
    resultNotes: ""
  });

  const updateServiceData = useCallback((updates: Partial<ServiceData>) => {
    setServiceData(prev => ({ ...prev, ...updates }));
  }, []);

  const validateDiagnosis = useCallback(() => {
    const { hairThickness, hairDensity, overallTone, overallUndertone, zoneColorAnalysis, zonePhysicalAnalysis } = serviceData;
    
    // Check if all required fields are filled
    const hasGeneralData = hairThickness && hairDensity && overallTone && overallUndertone;
    const hasAllZoneData = Object.values(zoneColorAnalysis).every(zone => 
      zone.depthLevel && zone.tone && zone.undertone && zone.state
    ) && Object.values(zonePhysicalAnalysis).every(zone =>
      zone.porosity && zone.elasticity && zone.resistance && zone.damage
    );
    
    return hasGeneralData && hasAllZoneData;
  }, [serviceData]);

  const goToNextStep = useCallback(() => {
    // If we're on diagnosis step, validate before continuing
    if (currentStep === 0 && !validateDiagnosis()) {
      Alert.alert(
        "Diagnóstico incompleto",
        "Por favor completa todos los campos requeridos antes de continuar.",
        [{ text: "OK" }]
      );
      return;
    }
    
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, validateDiagnosis]);

  const goToPreviousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      router.back();
    }
  }, [currentStep]);

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < STEPS.length) {
      setCurrentStep(stepIndex);
    }
  }, []);

  const canNavigateToStep = useCallback((stepIndex: number) => {
    // Can always go back to previous steps
    if (stepIndex <= currentStep) return true;
    
    // For forward navigation, check if current step is valid
    if (stepIndex === 1) return validateDiagnosis();
    if (stepIndex === 2) return validateDiagnosis() && serviceData.desiredAnalysisResult;
    if (stepIndex === 3) return validateDiagnosis() && serviceData.desiredAnalysisResult && serviceData.formula;
    
    return false;
  }, [currentStep, validateDiagnosis, serviceData]);

  return {
    currentStep,
    serviceData,
    updateServiceData,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    canNavigateToStep,
    validateDiagnosis,
    STEPS
  };
};
