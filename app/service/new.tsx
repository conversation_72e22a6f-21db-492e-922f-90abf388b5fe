import React, { useState, useEffect } from "react";
import { StyleSheet, View, Alert } from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import Colors from "@/constants/colors";
import { useClientStore } from "@/stores/client-store";
import { useClientHistoryStore } from "@/stores/client-history-store";
import { useAIAnalysisStore } from "@/stores/ai-analysis-store";
import Toast from "@/components/Toast";

// Import our new components and hooks
import { useServiceFlow, STEPS } from "./hooks/useServiceFlow";
import { useServicePersistence } from "./hooks/useServicePersistence";
import { ServiceHeader } from "./components/ServiceHeader";
import { StepIndicator } from "./components/StepIndicator";
import { DiagnosisStep } from "./components/DiagnosisStep";
import { DesiredColorStep } from "./components/DesiredColorStep";
import { FormulationStep } from "./components/FormulationStep";
import { CompletionStep } from "./components/CompletionStep";

export default function NewServiceScreen() {
  const { clientId, restoreDraft } = useLocalSearchParams();
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // Use our new hooks
  const {
    currentStep,
    serviceData,
    updateServiceData,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    canNavigateToStep
  } = useServiceFlow();

  const { saveServiceDraft, loadServiceDraft, deleteServiceDraft } = useServicePersistence();

  // Client Store
  const { getClient } = useClientStore();

  // Client History Store
  const { addServiceToHistory } = useClientHistoryStore();

  // AI Analysis Store (for passing to components)
  const { analysisResult } = useAIAnalysisStore();

  // Load client data on mount
  useEffect(() => {
    const loadClientData = async () => {
      if (clientId && typeof clientId === 'string') {
        try {
          const clientData = await getClient(clientId);
          if (clientData) {
            updateServiceData({
              client: clientData,
              clientId: clientId
            });

            // Try to load draft if requested
            if (restoreDraft === 'true') {
              const draft = loadServiceDraft(clientId);
              if (draft) {
                // Restore service data from draft
                updateServiceData({
                  ...draft.diagnosisData,
                  ...draft.desiredData,
                  ...draft.formulationData,
                  ...draft.resultData
                });

                showToastMessage("Borrador restaurado correctamente");
              }
            }
          }
        } catch (error) {
          console.error('Error loading client:', error);
          showToastMessage("Error al cargar datos del cliente");
        }
      }
    };

    loadClientData();
  }, [clientId, restoreDraft]);

  // Auto-save draft periodically
  useEffect(() => {
    if (serviceData.clientId && serviceData.client) {
      const autoSaveInterval = setInterval(() => {
        saveServiceDraft(serviceData, currentStep);
      }, 30000); // Auto-save every 30 seconds

      return () => clearInterval(autoSaveInterval);
    }
  }, [serviceData, currentStep]);

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  const handleSaveDraft = () => {
    if (serviceData.clientId) {
      saveServiceDraft(serviceData, currentStep);
      showToastMessage("Borrador guardado");
    }
  };

  const handleFinishService = async () => {
    try {
      // Add service to client history
      if (serviceData.clientId && serviceData.client) {
        await addServiceToHistory(serviceData.clientId, {
          date: new Date().toISOString(),
          type: 'color_service',
          formula: serviceData.formula,
          brand: serviceData.selectedBrand,
          line: serviceData.selectedLine,
          satisfaction: serviceData.clientSatisfaction,
          notes: serviceData.resultNotes,
          beforePhotos: serviceData.hairPhotos.map(p => p.uri),
          afterPhoto: serviceData.resultImage,
          technique: serviceData.desiredAnalysisResult?.general?.technique || 'color_service',
          cost: serviceData.formulaCost?.total || 0
        });

        // Delete draft after successful completion
        deleteServiceDraft(serviceData.clientId);

        showToastMessage("Servicio completado y guardado");

        // Navigate back after a short delay
        setTimeout(() => {
          router.back();
        }, 1500);
      }
    } catch (error) {
      console.error('Error finishing service:', error);
      Alert.alert("Error", "No se pudo completar el servicio. Por favor intenta nuevamente.");
    }
  };

  const handleStepNavigation = (stepIndex: number) => {
    if (canNavigateToStep(stepIndex)) {
      goToStep(stepIndex);
    } else {
      Alert.alert(
        "Paso no disponible",
        "Completa los pasos anteriores antes de continuar.",
        [{ text: "OK" }]
      );
    }
  };

  // Render the appropriate step component
  const renderCurrentStep = () => {
    const commonProps = {
      data: serviceData,
      onUpdate: updateServiceData,
      onNext: goToNextStep,
      onBack: goToPreviousStep,
      onSave: handleSaveDraft
    };

    switch (currentStep) {
      case 0:
        return <DiagnosisStep {...commonProps} />;
      case 1:
        return <DesiredColorStep {...commonProps} />;
      case 2:
        return (
          <FormulationStep
            {...commonProps}
            analysisResult={analysisResult}
          />
        );
      case 3:
        return (
          <CompletionStep
            {...commonProps}
            onNext={handleFinishService}
          />
        );
      default:
        return <DiagnosisStep {...commonProps} />;
    }
  };

  return (
    <View style={styles.container}>
      <ServiceHeader
        currentStep={currentStep}
        clientName={serviceData.client?.name}
        onSaveDraft={serviceData.clientId ? handleSaveDraft : undefined}
        onBack={goToPreviousStep}
      />

      <StepIndicator
        steps={STEPS}
        currentStep={currentStep}
        onStepPress={handleStepNavigation}
        canNavigateToStep={canNavigateToStep}
      />

      {renderCurrentStep()}

      <Toast
        visible={showToast}
        message={toastMessage}
        onHide={() => setShowToast(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
});